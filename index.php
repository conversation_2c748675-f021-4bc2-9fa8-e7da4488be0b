<?php
$pageTitle = 'الرئيسية';

// Performance optimizations - Set caching headers
$cacheTime = 3600; // 1 hour
header("Cache-Control: public, max-age=$cacheTime");
header("Expires: " . gmdate("D, d M Y H:i:s", time() + $cacheTime) . " GMT");
header("Last-Modified: " . gmdate("D, d M Y H:i:s", filemtime(__FILE__)) . " GMT");

// Enable compression
if (!ob_get_level()) {
    ob_start('ob_gzhandler');
}

require_once 'includes/header.php';

// التحقق من وجود جدول إعدادات الصفحة الرئيسية وإنشاؤه إذا لم يكن موجوداً
try {
    $tableExists = fetchOne("SHOW TABLES LIKE 'homepage_settings'");
    if (!$tableExists) {
        // إنشاء الجدول
        $createTableQuery = "
            CREATE TABLE `homepage_settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `section_name` varchar(100) NOT NULL,
                `setting_key` varchar(100) NOT NULL,
                `setting_value` text,
                `setting_type` enum('text','textarea','image','url','number','boolean') DEFAULT 'text',
                `sort_order` int(11) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_section_key` (`section_name`,`setting_key`),
                KEY `idx_section` (`section_name`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $pdo->exec($createTableQuery);

        // إدراج البيانات الافتراضية
        $defaultSettings = [
            // Carousel Settings
            ['carousel', 'slide_1_image', 'https://via.placeholder.com/1200x600/667eea/ffffff?text=الشريحة+الأولى', 'image', 1],
            ['carousel', 'slide_1_title', 'مرحباً بك في متجرنا الإلكتروني', 'text', 2],
            ['carousel', 'slide_1_subtitle', 'اكتشف مجموعة واسعة من المنتجات عالية الجودة', 'text', 3],
            ['carousel', 'slide_1_button_text', 'تصفح المنتجات', 'text', 4],
            ['carousel', 'slide_1_button_url', '/products.php', 'url', 5],
            ['carousel', 'slide_2_image', 'https://via.placeholder.com/1200x600/764ba2/ffffff?text=الشريحة+الثانية', 'image', 6],
            ['carousel', 'slide_3_image', 'https://via.placeholder.com/1200x600/28a745/ffffff?text=الشريحة+الثالثة', 'image', 7],
            ['carousel', 'slide_4_image', 'https://via.placeholder.com/1200x600/dc3545/ffffff?text=الشريحة+الرابعة', 'image', 8],
            ['carousel', 'auto_advance_time', '10', 'number', 9],
            ['carousel', 'show_indicators', '1', 'boolean', 10],
            ['carousel', 'show_controls', '1', 'boolean', 11],

            // Featured Products Settings
            ['featured_products', 'show_section', '1', 'boolean', 1],
            ['featured_products', 'section_title', 'المنتجات المميزة', 'text', 2],
            ['featured_products', 'section_subtitle', 'اكتشف أفضل منتجاتنا المختارة بعناية', 'text', 3],
            ['featured_products', 'products_limit', '6', 'number', 4],

            // Offers Settings
            ['offers', 'show_section', '1', 'boolean', 1],
            ['offers', 'section_title', 'العروض الخاصة', 'text', 2],
            ['offers', 'section_subtitle', 'لا تفوت عروضنا المحدودة والحصرية', 'text', 3],
            ['offers', 'banner_image', 'https://via.placeholder.com/1200x300/dc3545/ffffff?text=عروض+خاصة', 'image', 4],
            ['offers', 'banner_title', 'خصومات تصل إلى 50%', 'text', 5],
            ['offers', 'banner_subtitle', 'على مجموعة مختارة من المنتجات', 'text', 6],
            ['offers', 'banner_button_text', 'تصفح العروض', 'text', 7],
            ['offers', 'banner_button_url', '/offers.php', 'url', 8]
        ];

        $insertQuery = "INSERT INTO homepage_settings (section_name, setting_key, setting_value, setting_type, sort_order) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertQuery);

        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
    }
} catch (Exception $e) {
    error_log("Homepage settings table creation error: " . $e->getMessage());
}

// دالة احتياطية لجلب إعدادات الصفحة الرئيسية
if (!function_exists('getHomepageSectionSettings')) {
    function getHomepageSectionSettings($section) {
        try {
            global $pdo;
            $query = "SELECT setting_key, setting_value FROM homepage_settings WHERE section_name = ? AND is_active = 1";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$section]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $settings = [];
            foreach ($results as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
            return $settings;
        } catch (Exception $e) {
            return [];
        }
    }
}

// جلب إعدادات الصفحة الرئيسية مع القيم الافتراضية
$featuredProductsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'المنتجات المميزة',
    'section_subtitle' => 'اكتشف أفضل منتجاتنا المختارة بعناية',
    'products_limit' => '6'
], getHomepageSectionSettings('featured_products'));

$offersSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'العروض الخاصة',
    'section_subtitle' => 'لا تفوت عروضنا المحدودة والحصرية',
    'banner_image' => 'https://via.placeholder.com/1200x300/dc3545/ffffff?text=عروض+خاصة',
    'banner_title' => 'خصومات تصل إلى 50%',
    'banner_subtitle' => 'على مجموعة مختارة من المنتجات',
    'banner_button_text' => 'تصفح العروض',
    'banner_button_url' => '/offers.php'
], getHomepageSectionSettings('offers'));

$influencersSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'المؤثرين',
    'section_subtitle' => 'شاهد تجارب المؤثرين مع منتجاتنا',
    'content_limit' => '6'
], getHomepageSectionSettings('influencers'));

$reviewsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'آراء العملاء',
    'section_subtitle' => 'ماذا يقول عملاؤنا عن تجربتهم معنا',
    'reviews_limit' => '6'
], getHomepageSectionSettings('customer_reviews'));

$storySettings = array_merge([
    'show_section' => '1',
    'section_title' => 'قصة نجاح متجر Care',
    'story_content' => 'منذ تأسيسنا، نسعى لتقديم أفضل المنتجات وأعلى مستويات الخدمة لعملائنا الكرام.',
    'story_image' => 'https://via.placeholder.com/600x400/17a2b8/ffffff?text=قصة+النجاح',
    'achievements_customers' => '10000+',
    'achievements_products' => '500+',
    'achievements_years' => '5+'
], getHomepageSectionSettings('success_story'));

$whyChooseUsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'لماذا تختار متجري الإلكتروني؟',
    'section_subtitle' => 'نحن نقدم أفضل تجربة تسوق إلكتروني في المنطقة',
    'feature_1_icon' => 'bi-award',
    'feature_1_title' => 'جودة عالية',
    'feature_1_description' => 'نختار منتجاتنا بعناية فائقة لضمان أعلى مستويات الجودة والأصالة',
    'feature_2_icon' => 'bi-truck',
    'feature_2_title' => 'توصيل سريع',
    'feature_2_description' => 'نوفر خدمة توصيل سريعة وآمنة إلى جميع أنحاء العراق',
    'feature_3_icon' => 'bi-shield-check',
    'feature_3_title' => 'ضمان الجودة',
    'feature_3_description' => 'جميع منتجاتنا مضمونة مع إمكانية الإرجاع والاستبدال',
    'feature_4_icon' => 'bi-headset',
    'feature_4_title' => 'دعم 24/7',
    'feature_4_description' => 'فريق خدمة العملاء متاح على مدار الساعة لمساعدتك'
], getHomepageSectionSettings('why_choose_us'));

$newsletterSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'اشترك في النشرة البريدية',
    'section_subtitle' => 'احصل على آخر العروض والمنتجات الجديدة',
    'background_image' => 'https://via.placeholder.com/1200x300/343a40/ffffff?text=النشرة+البريدية',
    'placeholder_text' => 'أدخل بريدك الإلكتروني',
    'button_text' => 'اشترك الآن'
], getHomepageSectionSettings('newsletter'));

$ctaSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'ابدأ التسوق الآن!',
    'section_subtitle' => 'اكتشف آلاف المنتجات المميزة واستمتع بتجربة تسوق لا تُنسى',
    'background_color' => '#343a40',
    'primary_button_text' => 'تصفح المنتجات',
    'primary_button_url' => '/products.php',
    'secondary_button_text' => 'تواصل معنا',
    'secondary_button_url' => '/contact.php'
], getHomepageSectionSettings('call_to_action'));

// دالة مساعدة للتحقق من وجود الجدول
function tableExists($tableName) {
    try {
        $result = fetchOne("SHOW TABLES LIKE '$tableName'");
        return !empty($result);
    } catch (Exception $e) {
        return false;
    }
}

// دالة احتياطية لتنسيق التاريخ بالعربية
if (!function_exists('formatArabicDate')) {
    function formatArabicDate($date) {
        return date('Y/m/d', strtotime($date));
    }
}

// جلب المنتجات المميزة
$featuredProductsLimit = isset($featuredProductsSettings['products_limit']) ? (int)$featuredProductsSettings['products_limit'] : 6;
$featuredProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.is_featured = 1 AND p.status = 'active' 
    ORDER BY p.created_at DESC 
    LIMIT $featuredProductsLimit
");

// جلب المنتجات المخفضة للعروض
$discountedProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.discount > 0 AND p.status = 'active' 
    ORDER BY p.discount DESC 
    LIMIT 6
");

// جلب محتوى المؤثرين
$influencersLimit = isset($influencersSettings['content_limit']) ? (int)$influencersSettings['content_limit'] : 6;
$influencersContent = [];
if (tableExists('influencers_content')) {
    $influencersContent = fetchAll("
        SELECT * FROM influencers_content 
        WHERE status = 'published' 
        ORDER BY is_featured DESC, created_at DESC 
        LIMIT $influencersLimit
    ");
}

// جلب التقييمات المعتمدة
$reviewsLimit = isset($reviewsSettings['reviews_limit']) ? (int)$reviewsSettings['reviews_limit'] : 6;
$reviews = fetchAll("
    SELECT r.*, p.name as product_name
    FROM reviews r
    LEFT JOIN products p ON r.product_id = p.id
    WHERE r.status = 'approved'
    ORDER BY r.created_at DESC
    LIMIT $reviewsLimit
");

// جلب الفئات المميزة للعرض
$featuredCategories = fetchAll("
    SELECT c.*, COUNT(p.id) as products_count
    FROM categories c
    LEFT JOIN products p ON c.id = p.category_id AND p.status = 'active'
    WHERE c.status = 'active'
    GROUP BY c.id
    ORDER BY products_count DESC, c.name ASC
    LIMIT 6
");

// إعدادات الفئات المميزة
$categoriesSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'تصفح الفئات',
    'section_subtitle' => 'اكتشف مجموعة واسعة من الفئات المتنوعة'
], getHomepageSectionSettings('featured_categories'));

// إعدادات البانرات الترويجية
$promoBannerSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'عروض حصرية',
    'banner_1_image' => 'https://via.placeholder.com/600x300/28a745/ffffff?text=عرض+خاص+1',
    'banner_1_title' => 'خصم 30% على جميع المنتجات',
    'banner_1_subtitle' => 'لفترة محدودة فقط',
    'banner_1_url' => '/products.php',
    'banner_2_image' => 'https://via.placeholder.com/600x300/dc3545/ffffff?text=عرض+خاص+2',
    'banner_2_title' => 'شحن مجاني',
    'banner_2_subtitle' => 'على الطلبات أكثر من 50,000 دينار',
    'banner_2_url' => '/products.php'
], getHomepageSectionSettings('promo_banners'));

// إعدادات وسائل التواصل الاجتماعي
$socialMediaSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'تابعنا على وسائل التواصل',
    'section_subtitle' => 'ابق على اطلاع بآخر العروض والمنتجات الجديدة',
    'facebook_url' => '#',
    'instagram_url' => '#',
    'twitter_url' => '#',
    'youtube_url' => '#',
    'whatsapp_url' => '#',
    'telegram_url' => '#'
], getHomepageSectionSettings('social_media'));
?>

<!-- Professional Breadcrumb Navigation -->
<nav class="breadcrumb-nav" aria-label="breadcrumb">
    <div class="container">
        <ol class="breadcrumb">
            <li class="breadcrumb-item active" aria-current="page">
                <i class="bi bi-house-fill"></i> الصفحة الرئيسية
            </li>
        </ol>
    </div>
</nav>

<!-- Currency Converter Widget -->
<div class="currency-widget" id="currencyWidget">
    <div class="container">
        <div class="currency-converter">
            <div class="currency-info">
                <i class="bi bi-currency-exchange"></i>
                <span class="currency-text">العملة: دينار عراقي</span>
            </div>
            <div class="region-selector">
                <select class="form-select form-select-sm" id="regionSelector" onchange="updateRegionPricing()">
                    <option value="baghdad">بغداد</option>
                    <option value="basra">البصرة</option>
                    <option value="nineveh">نينوى</option>
                    <option value="erbil">أربيل</option>
                    <option value="najaf">النجف</option>
                    <option value="karbala">كربلاء</option>
                    <option value="anbar">الأنبار</option>
                    <option value="dhi_qar">ذي قار</option>
                    <option value="babylon">بابل</option>
                    <option value="kirkuk">كركوك</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Professional Hero Carousel Section -->
<?php include 'includes/homepage_carousel.php'; ?>

<!-- Professional Section Divider -->
<div class="section-divider divider-gradient"></div>

<!-- Featured Categories Section -->
<?php if (isset($categoriesSettings['show_section']) && $categoriesSettings['show_section'] == '1' && !empty($featuredCategories)): ?>
<section class="homepage-section bg-white animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo htmlspecialchars($categoriesSettings['section_title'] ?? 'تصفح الفئات'); ?></h2>
            <p class="section-subtitle"><?php echo htmlspecialchars($categoriesSettings['section_subtitle'] ?? 'اكتشف مجموعة واسعة من الفئات المتنوعة'); ?></p>
        </div>

        <div class="row">
            <?php foreach ($featuredCategories as $index => $category): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="category-card interactive-card animate-on-scroll delay-<?php echo ($index % 3) + 1; ?> h-100">
                        <div class="category-image-container">
                            <?php
                            $categoryImage = !empty($category['image_url']) ? $category['image_url'] : 'https://via.placeholder.com/400x250/667eea/ffffff?text=' . urlencode($category['name']);
                            ?>
                            <img src="<?php echo $categoryImage; ?>"
                                 class="category-image"
                                 alt="<?php echo htmlspecialchars($category['name']); ?>"
                                 loading="lazy">
                            <div class="category-overlay">
                                <div class="category-content">
                                    <h4 class="category-title"><?php echo htmlspecialchars($category['name']); ?></h4>
                                    <p class="category-count"><?php echo $category['products_count']; ?> منتج</p>
                                    <a href="<?php echo SITE_URL; ?>/products.php?category=<?php echo $category['id']; ?>"
                                       class="btn btn-light btn-sm">
                                        <i class="bi bi-arrow-left"></i> تصفح الفئة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-dots"></div>
<?php endif; ?>

<!-- Promotional Banners Section -->
<?php if (isset($promoBannerSettings['show_section']) && $promoBannerSettings['show_section'] == '1'): ?>
<section class="homepage-section bg-light animate-on-scroll">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title arabic-title"><?php echo htmlspecialchars($promoBannerSettings['section_title'] ?? 'عروض حصرية'); ?></h2>
            <p class="section-subtitle arabic-subtitle">اكتشف أحدث العروض والخصومات المميزة</p>
            <div class="arabic-date mt-2"></div>
        </div>

        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="promo-banner animate-on-scroll delay-1">
                    <img src="<?php echo htmlspecialchars($promoBannerSettings['banner_1_image']); ?>"
                         class="promo-banner-image"
                         alt="عرض ترويجي 1"
                         loading="lazy">
                    <div class="promo-banner-content">
                        <h3 class="promo-banner-title"><?php echo htmlspecialchars($promoBannerSettings['banner_1_title'] ?? 'خصم 30% على جميع المنتجات'); ?></h3>
                        <p class="promo-banner-subtitle"><?php echo htmlspecialchars($promoBannerSettings['banner_1_subtitle'] ?? 'لفترة محدودة فقط'); ?></p>
                        <a href="<?php echo SITE_URL . htmlspecialchars($promoBannerSettings['banner_1_url'] ?? '/products.php'); ?>"
                           class="btn btn-warning">
                            <i class="bi bi-percent"></i> اكتشف العرض
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="promo-banner animate-on-scroll delay-2">
                    <img src="<?php echo htmlspecialchars($promoBannerSettings['banner_2_image']); ?>"
                         class="promo-banner-image"
                         alt="عرض ترويجي 2"
                         loading="lazy">
                    <div class="promo-banner-content">
                        <h3 class="promo-banner-title"><?php echo htmlspecialchars($promoBannerSettings['banner_2_title'] ?? 'شحن مجاني'); ?></h3>
                        <p class="promo-banner-subtitle"><?php echo htmlspecialchars($promoBannerSettings['banner_2_subtitle'] ?? 'على الطلبات أكثر من 50,000 دينار'); ?></p>
                        <a href="<?php echo SITE_URL . htmlspecialchars($promoBannerSettings['banner_2_url'] ?? '/products.php'); ?>"
                           class="btn btn-success">
                            <i class="bi bi-truck"></i> تسوق الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-wave"></div>
<?php endif; ?>

<!-- 1. المنتجات المميزة (Featured Products) -->
<?php if (isset($featuredProductsSettings['show_section']) && $featuredProductsSettings['show_section'] == '1' && !empty($featuredProducts)): ?>
<section class="homepage-section bg-white animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo htmlspecialchars($featuredProductsSettings['section_title'] ?? 'المنتجات المميزة'); ?></h2>
            <p class="section-subtitle"><?php echo htmlspecialchars($featuredProductsSettings['section_subtitle'] ?? 'اكتشف أفضل منتجاتنا المختارة بعناية'); ?></p>
        </div>
        
        <div class="row">
            <?php foreach ($featuredProducts as $index => $product): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="product-card interactive-card animate-on-scroll delay-<?php echo ($index % 3) + 1; ?> h-100 position-relative">
                        <?php if ($product['discount'] > 0): ?>
                            <span class="discount-badge">خصم <?php echo $product['discount']; ?>%</span>
                        <?php endif; ?>

                        <!-- Quick Action Buttons -->
                        <div class="quick-actions" role="group" aria-label="إجراءات سريعة للمنتج">
                            <button class="quick-action-btn"
                                    onclick="addToWishlist(<?php echo $product['id']; ?>)"
                                    title="إضافة للمفضلة"
                                    aria-label="إضافة <?php echo htmlspecialchars($product['name']); ?> للمفضلة">
                                <i class="bi bi-heart" aria-hidden="true"></i>
                            </button>
                            <button class="quick-action-btn"
                                    onclick="addToCompare(<?php echo $product['id']; ?>)"
                                    title="إضافة للمقارنة"
                                    aria-label="إضافة <?php echo htmlspecialchars($product['name']); ?> للمقارنة">
                                <i class="bi bi-arrow-left-right" aria-hidden="true"></i>
                            </button>
                            <button class="quick-action-btn"
                                    onclick="quickView(<?php echo $product['id']; ?>)"
                                    title="عرض سريع"
                                    aria-label="عرض سريع لـ <?php echo htmlspecialchars($product['name']); ?>">
                                <i class="bi bi-eye" aria-hidden="true"></i>
                            </button>
                        </div>

                        <?php
                        // البحث عن أول صورة متاحة من الصور الخمس
                        $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                        for ($i = 1; $i <= 5; $i++) {
                            if (!empty($product['image_url_' . $i])) {
                                $imageUrl = $product['image_url_' . $i];
                                break;
                            }
                        }
                        // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                        if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
                            $imageUrl = UPLOAD_URL . '/' . $product['image'];
                        }
                        ?>
                        <div class="product-image-container">
                            <img src="<?php echo $imageUrl; ?>"
                                 class="card-img-top"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 loading="lazy"
                                 onerror="this.src='https://via.placeholder.com/300x250/f8f9fa/6c757d?text=صورة+غير+متاحة'">
                        </div>

                        <!-- Structured Data for Product -->
                        <script type="application/ld+json">
                        {
                            "@context": "https://schema.org",
                            "@type": "Product",
                            "name": "<?php echo htmlspecialchars($product['name']); ?>",
                            "description": "<?php echo htmlspecialchars($product['short_description']); ?>",
                            "image": "<?php echo $imageUrl; ?>",
                            "category": "<?php echo htmlspecialchars($product['category_name']); ?>",
                            "offers": {
                                "@type": "Offer",
                                "price": "<?php echo $product['discount'] > 0 ? ($product['price'] - ($product['price'] * $product['discount'] / 100)) : $product['price']; ?>",
                                "priceCurrency": "IQD",
                                "availability": "https://schema.org/InStock",
                                "url": "<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                            },
                            "brand": {
                                "@type": "Brand",
                                "name": "<?php echo htmlspecialchars($siteSettings['site_name'] ?? 'متجري الإلكتروني'); ?>"
                            }
                        }
                        </script>

                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                            </p>
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                            </p>

                            <div class="mt-auto">
                                <div class="price-section mb-3">
                                    <?php if ($product['discount'] > 0): ?>
                                        <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                        <span class="price-current"><?php echo formatPrice($discountedPrice); ?></span>
                                        <span class="price-original"><?php echo formatPrice($product['price']); ?></span>
                                        <span class="price-saved">وفر <?php echo formatPrice($product['price'] - $discountedPrice); ?></span>
                                    <?php else: ?>
                                        <span class="price-current text-primary"><?php echo formatPrice($product['price']); ?></span>
                                    <?php endif; ?>
                                </div>

                                <div class="product-actions" role="group" aria-label="إجراءات المنتج">
                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                       class="btn btn-outline-primary btn-product"
                                       aria-label="عرض تفاصيل <?php echo htmlspecialchars($product['name']); ?>">
                                        <i class="bi bi-eye" aria-hidden="true"></i> عرض
                                    </a>
                                    <button class="btn btn-primary btn-product"
                                            onclick="addToCart(<?php echo $product['id']; ?>, 1)"
                                            aria-label="إضافة <?php echo htmlspecialchars($product['name']); ?> إلى سلة التسوق">
                                        <i class="bi bi-cart-plus" aria-hidden="true"></i> أضف للسلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-5 animate-on-scroll">
            <a href="<?php echo SITE_URL; ?>/products.php" class="cta-btn cta-btn-primary">
                <i class="bi bi-grid"></i> عرض جميع المنتجات
            </a>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-dots"></div>
<?php endif; ?>

<!-- 2. العروض (Special Offers) -->
<?php if (isset($offersSettings['show_section']) && $offersSettings['show_section'] == '1'): ?>
<section class="homepage-section bg-light animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo htmlspecialchars($offersSettings['section_title'] ?? 'العروض الخاصة'); ?></h2>
            <p class="section-subtitle"><?php echo htmlspecialchars($offersSettings['section_subtitle'] ?? 'لا تفوت عروضنا المحدودة والحصرية'); ?></p>
        </div>

        <!-- Professional Offers Banner -->
        <?php if (!empty($offersSettings['banner_image'])): ?>
        <div class="row mb-5">
            <div class="col-12">
                <div class="offers-banner animate-on-scroll">
                    <img src="<?php echo htmlspecialchars($offersSettings['banner_image']); ?>"
                         class="w-100" style="height: 350px; object-fit: cover;" alt="عروض خاصة"
                         loading="lazy">
                    <div class="banner-content">
                        <h3 class="banner-title">
                            <?php echo htmlspecialchars($offersSettings['banner_title'] ?? 'خصومات تصل إلى 50%'); ?>
                        </h3>
                        <p class="banner-subtitle">
                            <?php echo htmlspecialchars($offersSettings['banner_subtitle'] ?? 'على مجموعة مختارة من المنتجات'); ?>
                        </p>
                        <a href="<?php echo SITE_URL . htmlspecialchars($offersSettings['banner_button_url'] ?? '/offers.php'); ?>"
                           class="banner-btn">
                            <i class="bi bi-percent"></i>
                            <?php echo htmlspecialchars($offersSettings['banner_button_text'] ?? 'تصفح العروض'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Professional Discounted Products -->
        <?php if (!empty($discountedProducts)): ?>
        <div class="row">
            <?php foreach ($discountedProducts as $index => $product): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="product-card interactive-card animate-on-scroll delay-<?php echo ($index % 3) + 1; ?> h-100 position-relative" style="border: 2px solid #dc3545;">
                        <span class="discount-badge">خصم <?php echo $product['discount']; ?>%</span>

                        <?php
                        // البحث عن أول صورة متاحة من الصور الخمس
                        $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                        for ($i = 1; $i <= 5; $i++) {
                            if (!empty($product['image_url_' . $i])) {
                                $imageUrl = $product['image_url_' . $i];
                                break;
                            }
                        }
                        // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                        if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
                            $imageUrl = UPLOAD_URL . '/' . $product['image'];
                        }
                        ?>
                        <img src="<?php echo $imageUrl; ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>"
                             loading="lazy">

                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                            </p>
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                            </p>

                            <div class="mt-auto">
                                <div class="price-section mb-3">
                                    <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                    <span class="price-current"><?php echo formatPrice($discountedPrice); ?></span>
                                    <span class="price-original"><?php echo formatPrice($product['price']); ?></span>
                                    <span class="price-saved">وفر <?php echo formatPrice($product['price'] - $discountedPrice); ?></span>
                                </div>

                                <div class="product-actions">
                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>"
                                       class="btn btn-outline-primary btn-product">
                                        <i class="bi bi-eye"></i> عرض
                                    </a>
                                    <button class="btn btn-primary btn-product"
                                            onclick="addToCart(<?php echo $product['id']; ?>, 1)">
                                        <i class="bi bi-cart-plus"></i> أضف للسلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-wave"></div>
<?php endif; ?>

<!-- Styles moved to assets/css/homepage.css for better organization -->

<script>
// Add to cart function
function addToCart(productId, quantity = 1) {
    fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=add&product_id=${productId}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم إضافة المنتج إلى السلة بنجاح!', 'success');
            // Update cart count if element exists
            const cartBadge = document.querySelector('.cart-badge');
            if (cartBadge && data.cart_count) {
                cartBadge.textContent = data.cart_count;
                cartBadge.style.display = 'flex';
            }
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
</script>

<!-- 3. المؤثرين (Influencers) -->
<?php if (isset($influencersSettings['show_section']) && $influencersSettings['show_section'] == '1' && !empty($influencersContent)): ?>
<section class="homepage-section bg-white animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo htmlspecialchars($influencersSettings['section_title'] ?? 'المؤثرين'); ?></h2>
            <p class="section-subtitle"><?php echo htmlspecialchars($influencersSettings['section_subtitle'] ?? 'شاهد تجارب المؤثرين مع منتجاتنا'); ?></p>
        </div>

        <div class="row">
            <?php foreach ($influencersContent as $index => $content): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="influencer-card interactive-card animate-on-scroll delay-<?php echo ($index % 3) + 1; ?> h-100">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <?php if (!empty($content['influencer_image'])): ?>
                                    <img src="<?php echo htmlspecialchars($content['influencer_image']); ?>"
                                         class="influencer-avatar me-3" alt="<?php echo htmlspecialchars($content['influencer_name']); ?>"
                                         loading="lazy">
                                <?php else: ?>
                                    <div class="influencer-avatar me-3 d-flex align-items-center justify-content-center bg-white text-primary">
                                        <i class="bi bi-person-circle fs-2"></i>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <h6 class="mb-0 text-white"><?php echo htmlspecialchars($content['influencer_name']); ?></h6>
                                    <small class="text-white-50"><?php echo ucfirst($content['content_type']); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($content['content_title'])): ?>
                                <h6 class="card-title"><?php echo htmlspecialchars($content['content_title']); ?></h6>
                            <?php endif; ?>
                            <p class="card-text"><?php echo htmlspecialchars(substr($content['content_text'], 0, 150)) . '...'; ?></p>

                            <?php if ($content['content_type'] === 'review' && !empty($content['rating'])): ?>
                                <div class="rating mb-3">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="bi bi-star<?php echo $i <= $content['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($content['video_url'])): ?>
                                <a href="<?php echo htmlspecialchars($content['video_url']); ?>"
                                   class="btn btn-primary btn-sm" target="_blank">
                                    <i class="bi bi-play-circle"></i> مشاهدة الفيديو
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-5 animate-on-scroll">
            <a href="<?php echo SITE_URL; ?>/influencers.php" class="cta-btn cta-btn-primary">
                <i class="bi bi-people"></i> عرض جميع المؤثرين
            </a>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-gradient"></div>
<?php endif; ?>

<!-- 4. آراء العملاء (Customer Reviews) -->
<?php if (isset($reviewsSettings['show_section']) && $reviewsSettings['show_section'] == '1' && !empty($reviews)): ?>
<section class="homepage-section bg-light animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo htmlspecialchars($reviewsSettings['section_title'] ?? 'آراء العملاء'); ?></h2>
            <p class="section-subtitle"><?php echo htmlspecialchars($reviewsSettings['section_subtitle'] ?? 'ماذا يقول عملاؤنا عن تجربتهم معنا'); ?></p>
        </div>

        <!-- Enhanced Testimonial Carousel -->
        <div class="testimonial-carousel-container">
            <div class="testimonial-carousel" id="testimonialCarousel">
                <div class="testimonial-track">
                    <?php foreach ($reviews as $index => $review): ?>
                        <div class="testimonial-slide">
                            <div class="review-card interactive-card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="review-avatar me-3">
                                            <i class="bi bi-person-circle"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($review['customer_name']); ?></h6>
                                            <div class="rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <p class="card-text"><?php echo htmlspecialchars($review['comment']); ?></p>

                                    <?php if (!empty($review['product_name'])): ?>
                                        <div class="mt-3 pt-3 border-top">
                                            <small class="text-muted">
                                                <i class="bi bi-box"></i> <?php echo htmlspecialchars($review['product_name']); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer bg-transparent border-0">
                                    <small class="text-muted">
                                        <i class="bi bi-calendar"></i> <?php echo formatArabicDate($review['created_at']); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Carousel Controls -->
            <div class="carousel-controls">
                <button class="carousel-btn carousel-prev" onclick="moveTestimonialCarousel(-1)">
                    <i class="bi bi-chevron-right"></i>
                </button>
                <button class="carousel-btn carousel-next" onclick="moveTestimonialCarousel(1)">
                    <i class="bi bi-chevron-left"></i>
                </button>
            </div>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php for ($i = 0; $i < count($reviews); $i++): ?>
                    <button class="carousel-indicator <?php echo $i === 0 ? 'active' : ''; ?>"
                            onclick="goToTestimonialSlide(<?php echo $i; ?>)"></button>
                <?php endfor; ?>
            </div>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-dots"></div>
<?php endif; ?>

<!-- 5. قصة نجاح متجر Care (Success Story) -->
<?php if (isset($storySettings['show_section']) && $storySettings['show_section'] == '1'): ?>
<section class="homepage-section bg-white animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo htmlspecialchars($storySettings['section_title'] ?? 'قصة نجاح متجر Care'); ?></h2>
        </div>

        <div class="row align-items-center">
            <div class="col-lg-6 mb-4">
                <div class="animate-on-scroll">
                    <p class="lead mb-4" style="font-size: 1.3rem; line-height: 1.8; color: #2c3e50;">
                        <?php echo htmlspecialchars($storySettings['story_content'] ?? 'منذ تأسيسنا، نسعى لتقديم أفضل المنتجات وأعلى مستويات الخدمة لعملائنا الكرام.'); ?>
                    </p>
                </div>

                <div class="row text-center mt-5">
                    <div class="col-4">
                        <div class="achievement-item animate-on-scroll delay-1">
                            <h3 class="text-primary"><?php echo htmlspecialchars($storySettings['achievements_customers'] ?? '10000+'); ?></h3>
                            <p>عميل سعيد</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="achievement-item animate-on-scroll delay-2">
                            <h3 class="text-success"><?php echo htmlspecialchars($storySettings['achievements_products'] ?? '500+'); ?></h3>
                            <p>منتج متنوع</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="achievement-item animate-on-scroll delay-3">
                            <h3 class="text-info"><?php echo htmlspecialchars($storySettings['achievements_years'] ?? '5+'); ?></h3>
                            <p>سنوات خبرة</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="animate-on-scroll">
                    <img src="<?php echo htmlspecialchars($storySettings['story_image'] ?? 'https://via.placeholder.com/600x400/17a2b8/ffffff?text=قصة+النجاح'); ?>"
                         class="img-fluid rounded-4 shadow-lg" alt="قصة النجاح"
                         loading="lazy"
                         style="border-radius: 25px !important; box-shadow: 0 20px 60px rgba(0,0,0,0.15) !important;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-wave"></div>
<?php endif; ?>

<!-- 6. لماذا تختار متجري الإلكتروني؟ (Why Choose Us) -->
<?php if (isset($whyChooseUsSettings['show_section']) && $whyChooseUsSettings['show_section'] == '1'): ?>
<section class="homepage-section bg-light animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo htmlspecialchars($whyChooseUsSettings['section_title'] ?? 'لماذا تختار متجري الإلكتروني؟'); ?></h2>
            <p class="section-subtitle"><?php echo htmlspecialchars($whyChooseUsSettings['section_subtitle'] ?? 'نحن نقدم أفضل تجربة تسوق إلكتروني في المنطقة'); ?></p>
        </div>

        <div class="row">
            <!-- Feature 1 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex animate-on-scroll delay-1">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-wrapper bg-primary">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_1_icon'] ?? 'bi-award'); ?> text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-3"><?php echo htmlspecialchars($whyChooseUsSettings['feature_1_title'] ?? 'جودة عالية'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_1_description'] ?? 'نختار منتجاتنا بعناية فائقة لضمان أعلى مستويات الجودة والأصالة'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex animate-on-scroll delay-2">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-wrapper bg-success">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_2_icon'] ?? 'bi-truck'); ?> text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-3"><?php echo htmlspecialchars($whyChooseUsSettings['feature_2_title'] ?? 'توصيل سريع'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_2_description'] ?? 'نوفر خدمة توصيل سريعة وآمنة إلى جميع أنحاء العراق'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex animate-on-scroll delay-3">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-wrapper bg-info">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_3_icon'] ?? 'bi-shield-check'); ?> text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-3"><?php echo htmlspecialchars($whyChooseUsSettings['feature_3_title'] ?? 'ضمان الجودة'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_3_description'] ?? 'جميع منتجاتنا مضمونة مع إمكانية الإرجاع والاستبدال'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Feature 4 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex animate-on-scroll delay-1">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-wrapper bg-warning">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_4_icon'] ?? 'bi-headset'); ?> text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-3"><?php echo htmlspecialchars($whyChooseUsSettings['feature_4_title'] ?? 'دعم 24/7'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_4_description'] ?? 'فريق خدمة العملاء متاح على مدار الساعة لمساعدتك'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-gradient"></div>
<?php endif; ?>

<!-- 7. اشترك في النشرة البريدية (Newsletter) -->
<?php if (isset($newsletterSettings['show_section']) && $newsletterSettings['show_section'] == '1'): ?>
<section class="newsletter-section animate-on-scroll"
         style="background-image: url('<?php echo htmlspecialchars($newsletterSettings['background_image'] ?? 'https://via.placeholder.com/1200x300/343a40/ffffff?text=النشرة+البريدية'); ?>');">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
                <h2 class="section-title text-white mb-4"><?php echo htmlspecialchars($newsletterSettings['section_title'] ?? 'اشترك في النشرة البريدية'); ?></h2>
                <p class="section-subtitle text-white-50 mb-5"><?php echo htmlspecialchars($newsletterSettings['section_subtitle'] ?? 'احصل على آخر العروض والمنتجات الجديدة'); ?></p>

                <form id="homeNewsletterForm" class="newsletter-form">
                    <div class="row g-3 justify-content-center">
                        <div class="col-md-7">
                            <input type="email" class="form-control" name="email"
                                   placeholder="<?php echo htmlspecialchars($newsletterSettings['placeholder_text'] ?? 'أدخل بريدك الإلكتروني'); ?>" required>
                        </div>
                        <div class="col-md-5">
                            <button type="submit" class="btn w-100">
                                <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($newsletterSettings['button_text'] ?? 'اشترك الآن'); ?>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-dots"></div>
<?php endif; ?>

<!-- Social Media Integration Section -->
<?php if (isset($socialMediaSettings['show_section']) && $socialMediaSettings['show_section'] == '1'): ?>
<section class="homepage-section bg-gradient-primary animate-on-scroll">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title text-white"><?php echo htmlspecialchars($socialMediaSettings['section_title'] ?? 'تابعنا على وسائل التواصل'); ?></h2>
            <p class="section-subtitle text-white-50"><?php echo htmlspecialchars($socialMediaSettings['section_subtitle'] ?? 'ابق على اطلاع بآخر العروض والمنتجات الجديدة'); ?></p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="social-media-grid">
                    <?php if (!empty($socialMediaSettings['facebook_url']) && $socialMediaSettings['facebook_url'] !== '#'): ?>
                    <a href="<?php echo htmlspecialchars($socialMediaSettings['facebook_url']); ?>"
                       class="social-media-item animate-on-scroll delay-1"
                       target="_blank"
                       style="background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);">
                        <i class="bi bi-facebook"></i>
                        <span>Facebook</span>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($socialMediaSettings['instagram_url']) && $socialMediaSettings['instagram_url'] !== '#'): ?>
                    <a href="<?php echo htmlspecialchars($socialMediaSettings['instagram_url']); ?>"
                       class="social-media-item animate-on-scroll delay-2"
                       target="_blank"
                       style="background: linear-gradient(135deg, #e4405f 0%, #f56040 100%);">
                        <i class="bi bi-instagram"></i>
                        <span>Instagram</span>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($socialMediaSettings['whatsapp_url']) && $socialMediaSettings['whatsapp_url'] !== '#'): ?>
                    <a href="<?php echo htmlspecialchars($socialMediaSettings['whatsapp_url']); ?>"
                       class="social-media-item animate-on-scroll delay-3"
                       target="_blank"
                       style="background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);">
                        <i class="bi bi-whatsapp"></i>
                        <span>WhatsApp</span>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($socialMediaSettings['youtube_url']) && $socialMediaSettings['youtube_url'] !== '#'): ?>
                    <a href="<?php echo htmlspecialchars($socialMediaSettings['youtube_url']); ?>"
                       class="social-media-item animate-on-scroll delay-1"
                       target="_blank"
                       style="background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);">
                        <i class="bi bi-youtube"></i>
                        <span>YouTube</span>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($socialMediaSettings['telegram_url']) && $socialMediaSettings['telegram_url'] !== '#'): ?>
                    <a href="<?php echo htmlspecialchars($socialMediaSettings['telegram_url']); ?>"
                       class="social-media-item animate-on-scroll delay-2"
                       target="_blank"
                       style="background: linear-gradient(135deg, #0088cc 0%, #005580 100%);">
                        <i class="bi bi-telegram"></i>
                        <span>Telegram</span>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($socialMediaSettings['twitter_url']) && $socialMediaSettings['twitter_url'] !== '#'): ?>
                    <a href="<?php echo htmlspecialchars($socialMediaSettings['twitter_url']); ?>"
                       class="social-media-item animate-on-scroll delay-3"
                       target="_blank"
                       style="background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);">
                        <i class="bi bi-twitter"></i>
                        <span>Twitter</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Professional Section Divider -->
<div class="section-divider divider-wave"></div>
<?php endif; ?>

<!-- 8. ابدأ التسوق الآن! (Call to Action) -->
<?php if (isset($ctaSettings['show_section']) && $ctaSettings['show_section'] == '1'): ?>
<section class="cta-section animate-on-scroll" style="background-color: <?php echo htmlspecialchars($ctaSettings['background_color'] ?? '#343a40'); ?>;">
    <div class="container text-center">
        <h2 class="section-title text-white mb-4"><?php echo htmlspecialchars($ctaSettings['section_title'] ?? 'ابدأ التسوق الآن!'); ?></h2>
        <p class="section-subtitle text-white-50 mb-5"><?php echo htmlspecialchars($ctaSettings['section_subtitle'] ?? 'اكتشف آلاف المنتجات المميزة واستمتع بتجربة تسوق لا تُنسى'); ?></p>
        <div class="cta-buttons">
            <a href="<?php echo SITE_URL . htmlspecialchars($ctaSettings['primary_button_url'] ?? '/products.php'); ?>" class="cta-btn cta-btn-primary">
                <i class="bi bi-grid"></i> <?php echo htmlspecialchars($ctaSettings['primary_button_text'] ?? 'تصفح المنتجات'); ?>
            </a>
            <a href="<?php echo SITE_URL . htmlspecialchars($ctaSettings['secondary_button_url'] ?? '/contact.php'); ?>"
               class="cta-btn cta-btn-success">
                <i class="bi bi-whatsapp"></i> <?php echo htmlspecialchars($ctaSettings['secondary_button_text'] ?? 'تواصل معنا'); ?>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Professional styles now handled by assets/css/homepage.css -->

<script>
// Newsletter subscription
document.getElementById('homeNewsletterForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('<?php echo SITE_URL; ?>/ajax/newsletter.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم الاشتراك بنجاح في النشرة البريدية!', 'success');
            this.reset();
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });
});

// Professional Scroll Animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
            }
        });
    }, observerOptions);

    // Observe all elements with animation classes
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Hide page loader
    const pageLoader = document.getElementById('pageLoader');
    if (pageLoader) {
        setTimeout(() => {
            pageLoader.classList.add('fade-out');
            setTimeout(() => {
                pageLoader.style.display = 'none';
            }, 500);
        }, 1000);
    }

    initScrollAnimations();
    initImageOptimizations();
    initEnhancedInteractions();

    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Enhanced Image Optimizations with WebP Support
function initImageOptimizations() {
    // Check WebP support
    const supportsWebP = checkWebPSupport();

    // Progressive image loading with WebP support
    document.querySelectorAll('img[loading="lazy"]').forEach(img => {
        // Convert to WebP if supported and not already WebP
        if (supportsWebP && !img.src.includes('.webp')) {
            const webpSrc = convertToWebP(img.src);
            if (webpSrc !== img.src) {
                // Test if WebP version exists
                testImageExists(webpSrc).then(exists => {
                    if (exists) {
                        img.src = webpSrc;
                    }
                });
            }
        }
        // Create loading skeleton
        const skeleton = document.createElement('div');
        skeleton.className = 'loading-skeleton';
        skeleton.style.width = img.offsetWidth + 'px';
        skeleton.style.height = img.offsetHeight + 'px';
        skeleton.style.position = 'absolute';
        skeleton.style.top = '0';
        skeleton.style.left = '0';

        // Insert skeleton
        img.parentNode.style.position = 'relative';
        img.parentNode.insertBefore(skeleton, img);
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.5s ease';

        // Handle image load
        img.addEventListener('load', function() {
            this.style.opacity = '1';
            skeleton.remove();
        });

        // Handle image error with fallback
        img.addEventListener('error', function() {
            this.src = 'https://via.placeholder.com/400x250/f8f9fa/6c757d?text=صورة+غير+متاحة';
            this.style.opacity = '1';
            skeleton.remove();
        });
    });
}

// Enhanced Interactive Elements
function initEnhancedInteractions() {
    // Add interactive effects to cards
    document.querySelectorAll('.product-card, .category-card, .promo-banner').forEach(card => {
        card.classList.add('interactive-element');

        // Add click ripple effect
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.className = 'ripple-effect';
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255,255,255,0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.pointerEvents = 'none';

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
            ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Enhanced button interactions
    document.querySelectorAll('.btn').forEach(btn => {
        btn.classList.add('btn-enhanced');
    });

    // Floating WhatsApp button animation
    const whatsappBtn = document.querySelector('.whatsapp-btn');
    if (whatsappBtn) {
        // Add pulse animation on first visit
        setTimeout(() => {
            whatsappBtn.style.animation = 'pulse 2s infinite';
        }, 3000);

        // Remove pulse on hover
        whatsappBtn.addEventListener('mouseenter', function() {
            this.style.animation = 'none';
        });
    }
}

// Add CSS for ripple effect
const rippleCSS = `
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// Enhanced cart functionality with better UX
function addToCart(productId, quantity = 1) {
    // Add loading state to button
    const buttons = document.querySelectorAll(`button[onclick*="${productId}"]`);
    buttons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإضافة...';
    });

    fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=add&product_id=${productId}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم إضافة المنتج إلى السلة بنجاح!', 'success');
            // Update cart count if element exists
            const cartBadge = document.querySelector('.cart-badge');
            if (cartBadge && data.cart_count) {
                cartBadge.textContent = data.cart_count;
                cartBadge.style.display = 'flex';
                // Add animation to cart badge
                cartBadge.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    cartBadge.style.transform = 'scale(1)';
                }, 200);
            }
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    })
    .finally(() => {
        // Restore button state
        buttons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-cart-plus"></i> أضف للسلة';
        });
    });
}

// Quick Action Functions
function addToWishlist(productId) {
    // Add loading state
    const btn = event.target.closest('.quick-action-btn');
    const originalIcon = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
    btn.disabled = true;

    fetch('<?php echo SITE_URL; ?>/ajax/wishlist.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=add&product_id=${productId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            btn.innerHTML = '<i class="bi bi-heart-fill"></i>';
            btn.style.background = 'var(--danger-gradient)';
            btn.style.color = 'white';
            showToast('تم إضافة المنتج للمفضلة!', 'success');
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
            btn.innerHTML = originalIcon;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        btn.innerHTML = originalIcon;
    })
    .finally(() => {
        btn.disabled = false;
    });
}

function addToCompare(productId) {
    const btn = event.target.closest('.quick-action-btn');
    const originalIcon = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
    btn.disabled = true;

    fetch('<?php echo SITE_URL; ?>/ajax/compare.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=add&product_id=${productId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            btn.innerHTML = '<i class="bi bi-check2"></i>';
            btn.style.background = 'var(--success-gradient)';
            btn.style.color = 'white';
            showToast('تم إضافة المنتج للمقارنة!', 'success');
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
            btn.innerHTML = originalIcon;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        btn.innerHTML = originalIcon;
    })
    .finally(() => {
        btn.disabled = false;
    });
}

function quickView(productId) {
    const btn = event.target.closest('.quick-action-btn');
    const originalIcon = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
    btn.disabled = true;

    fetch('<?php echo SITE_URL; ?>/ajax/product_quick_view.php?id=' + productId)
    .then(response => response.text())
    .then(html => {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'quickViewModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">عرض سريع</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${html}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Remove modal when hidden
        modal.addEventListener('hidden.bs.modal', function() {
            modal.remove();
        });
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    })
    .finally(() => {
        btn.innerHTML = originalIcon;
        btn.disabled = false;
    });
}

// Enhanced Toast Notification System
function showToast(message, type = 'info') {
    // Remove existing toasts
    document.querySelectorAll('.toast-notification').forEach(toast => toast.remove());

    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="bi bi-x"></i>
        </button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Testimonial Carousel Functionality
let currentTestimonialSlide = 0;
let testimonialAutoPlayInterval;

function initTestimonialCarousel() {
    const carousel = document.getElementById('testimonialCarousel');
    if (!carousel) return;

    const track = carousel.querySelector('.testimonial-track');
    const slides = carousel.querySelectorAll('.testimonial-slide');
    const indicators = carousel.querySelectorAll('.carousel-indicator');

    if (slides.length === 0) return;

    // Start auto-play
    startTestimonialAutoPlay();

    // Pause on hover
    carousel.addEventListener('mouseenter', stopTestimonialAutoPlay);
    carousel.addEventListener('mouseleave', startTestimonialAutoPlay);
}

function moveTestimonialCarousel(direction) {
    const carousel = document.getElementById('testimonialCarousel');
    const slides = carousel.querySelectorAll('.testimonial-slide');
    const totalSlides = slides.length;

    currentTestimonialSlide += direction;

    if (currentTestimonialSlide >= totalSlides) {
        currentTestimonialSlide = 0;
    } else if (currentTestimonialSlide < 0) {
        currentTestimonialSlide = totalSlides - 1;
    }

    updateTestimonialCarousel();

    // Restart auto-play
    stopTestimonialAutoPlay();
    startTestimonialAutoPlay();
}

function goToTestimonialSlide(slideIndex) {
    currentTestimonialSlide = slideIndex;
    updateTestimonialCarousel();

    // Restart auto-play
    stopTestimonialAutoPlay();
    startTestimonialAutoPlay();
}

function updateTestimonialCarousel() {
    const carousel = document.getElementById('testimonialCarousel');
    if (!carousel) return;

    const track = carousel.querySelector('.testimonial-track');
    const indicators = carousel.querySelectorAll('.carousel-indicator');

    // Calculate slide width based on screen size
    let slideWidth = 100;
    if (window.innerWidth >= 992) {
        slideWidth = 33.333;
    } else if (window.innerWidth >= 768) {
        slideWidth = 50;
    }

    const translateX = -currentTestimonialSlide * slideWidth;
    track.style.transform = `translateX(${translateX}%)`;

    // Update indicators
    indicators.forEach((indicator, index) => {
        indicator.classList.toggle('active', index === currentTestimonialSlide);
    });
}

function startTestimonialAutoPlay() {
    const carousel = document.getElementById('testimonialCarousel');
    if (!carousel) return;

    const slides = carousel.querySelectorAll('.testimonial-slide');
    if (slides.length <= 1) return;

    testimonialAutoPlayInterval = setInterval(() => {
        moveTestimonialCarousel(1);
    }, 5000);
}

function stopTestimonialAutoPlay() {
    if (testimonialAutoPlayInterval) {
        clearInterval(testimonialAutoPlayInterval);
        testimonialAutoPlayInterval = null;
    }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initTestimonialCarousel();
    initSearchFunctionality();

    // Handle window resize for responsive carousel
    window.addEventListener('resize', updateTestimonialCarousel);
});

// Enhanced Search Functionality
function initSearchFunctionality() {
    const searchInput = document.querySelector('.search-input');
    const searchSuggestions = document.getElementById('searchSuggestions');

    if (!searchInput || !searchSuggestions) return;

    let searchTimeout;

    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        clearTimeout(searchTimeout);

        if (query.length < 2) {
            searchSuggestions.style.display = 'none';
            return;
        }

        searchTimeout = setTimeout(() => {
            fetchSearchSuggestions(query);
        }, 300);
    });

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
            searchSuggestions.style.display = 'none';
        }
    });
}

function fetchSearchSuggestions(query) {
    const searchSuggestions = document.getElementById('searchSuggestions');

    fetch(`<?php echo SITE_URL; ?>/ajax/search_suggestions.php?q=${encodeURIComponent(query)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.suggestions.length > 0) {
            let suggestionsHTML = '';
            data.suggestions.forEach(suggestion => {
                suggestionsHTML += `
                    <div class="search-suggestion" onclick="selectSearchSuggestion('${suggestion.name}')">
                        <i class="bi bi-search"></i>
                        <span>${suggestion.name}</span>
                    </div>
                `;
            });
            searchSuggestions.innerHTML = suggestionsHTML;
            searchSuggestions.style.display = 'block';
        } else {
            searchSuggestions.style.display = 'none';
        }
    })
    .catch(error => {
        console.error('Search suggestions error:', error);
        searchSuggestions.style.display = 'none';
    });
}

function selectSearchSuggestion(suggestion) {
    const searchInput = document.querySelector('.search-input');
    const searchSuggestions = document.getElementById('searchSuggestions');

    searchInput.value = suggestion;
    searchSuggestions.style.display = 'none';

    // Submit search form
    searchInput.closest('form').submit();
}

// WebP Support Functions
function checkWebPSupport() {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
}

function convertToWebP(imageSrc) {
    // Simple conversion - replace extension with .webp
    // In a real implementation, you'd have server-side WebP generation
    const extensions = ['.jpg', '.jpeg', '.png', '.gif'];
    for (const ext of extensions) {
        if (imageSrc.includes(ext)) {
            return imageSrc.replace(ext, '.webp');
        }
    }
    return imageSrc;
}

function testImageExists(imageSrc) {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = imageSrc;
    });
}

// Performance Monitoring
function initPerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', function() {
        if ('performance' in window) {
            const perfData = performance.getEntriesByType('navigation')[0];
            const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

            // Log performance data (in production, send to analytics)
            console.log('Page Load Time:', loadTime + 'ms');

            // Show performance warning if load time is too high
            if (loadTime > 3000) {
                console.warn('Page load time is high:', loadTime + 'ms');
            }
        }
    });

    // Monitor largest contentful paint
    if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log('LCP:', lastEntry.startTime);
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }
}

// Initialize performance monitoring
document.addEventListener('DOMContentLoaded', function() {
    initPerformanceMonitoring();
    initArabicFeatures();
});

// Arabic E-commerce Specific Features
function initArabicFeatures() {
    // Initialize Arabic date display
    updateArabicDates();

    // Initialize region-based pricing
    updateRegionPricing();

    // Initialize RTL animations
    initRTLAnimations();

    // Update dates every minute
    setInterval(updateArabicDates, 60000);
}

// Arabic Date and Time Functions
function updateArabicDates() {
    const dateElements = document.querySelectorAll('.arabic-date');
    const now = new Date();

    dateElements.forEach(element => {
        const gregorianDate = formatArabicDate(now);
        const hijriDate = getHijriDate(now);

        element.innerHTML = `
            <span class="hijri-date">${hijriDate}</span>
            <br>
            <span class="gregorian-date">${gregorianDate}</span>
        `;
    });
}

function formatArabicDate(date) {
    const arabicMonths = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const arabicDays = [
        'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
    ];

    const day = arabicDays[date.getDay()];
    const dayNum = date.getDate();
    const month = arabicMonths[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${dayNum} ${month} ${year}`;
}

function getHijriDate(date) {
    // Simplified Hijri date calculation (approximate)
    // In production, use a proper Hijri calendar library
    const hijriYear = Math.floor((date.getFullYear() - 622) * 1.030684);
    const hijriMonths = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];

    const monthIndex = Math.floor(Math.random() * 12); // Simplified for demo
    return `${Math.floor(Math.random() * 29) + 1} ${hijriMonths[monthIndex]} ${hijriYear}`;
}

// Region-based Pricing
function updateRegionPricing() {
    const regionSelector = document.getElementById('regionSelector');
    if (!regionSelector) return;

    const selectedRegion = regionSelector.value;
    const deliveryPrices = {
        'baghdad': 5000,
        'basra': 8000,
        'nineveh': 7000,
        'erbil': 9000,
        'najaf': 6000,
        'karbala': 6000,
        'anbar': 10000,
        'dhi_qar': 8000,
        'babylon': 6000,
        'kirkuk': 8000
    };

    const deliveryPrice = deliveryPrices[selectedRegion] || 7000;

    // Update delivery pricing display
    const pricingElements = document.querySelectorAll('.regional-pricing');
    pricingElements.forEach(element => {
        element.innerHTML = `
            <span class="region-name">التوصيل إلى ${getRegionName(selectedRegion)}:</span>
            <span class="delivery-cost">${formatPrice(deliveryPrice)}</span>
        `;
    });

    // Store selected region in localStorage
    localStorage.setItem('selectedRegion', selectedRegion);
}

function getRegionName(regionCode) {
    const regionNames = {
        'baghdad': 'بغداد',
        'basra': 'البصرة',
        'nineveh': 'نينوى',
        'erbil': 'أربيل',
        'najaf': 'النجف',
        'karbala': 'كربلاء',
        'anbar': 'الأنبار',
        'dhi_qar': 'ذي قار',
        'babylon': 'بابل',
        'kirkuk': 'كركوك'
    };

    return regionNames[regionCode] || regionCode;
}

// RTL Animations
function initRTLAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;

                // Add RTL-specific animation classes
                if (element.classList.contains('slide-in-right')) {
                    element.style.animation = 'slideInFromRight 0.6s ease-out';
                } else if (element.classList.contains('slide-in-left')) {
                    element.style.animation = 'slideInFromLeft 0.6s ease-out';
                }

                observer.unobserve(element);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe elements with RTL animation classes
    document.querySelectorAll('.slide-in-right, .slide-in-left').forEach(element => {
        observer.observe(element);
    });
}

// Arabic Number Formatting
function formatArabicNumbers(number) {
    // Convert Western numerals to Arabic-Indic numerals if needed
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/[0-9]/g, (digit) => arabicNumerals[digit]);
}

// Load saved region on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedRegion = localStorage.getItem('selectedRegion');
    if (savedRegion) {
        const regionSelector = document.getElementById('regionSelector');
        if (regionSelector) {
            regionSelector.value = savedRegion;
            updateRegionPricing();
        }
    }

    // Initialize advanced features
    initAdvancedFeatures();
});

// Advanced Interactive Features
function initAdvancedFeatures() {
    initScrollIndicator();
    initBackToTop();
    initParallaxEffects();
    initCounterAnimations();
    initAdvancedAnimations();
    initKeyboardNavigation();
}

// Scroll Progress Indicator
function initScrollIndicator() {
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.innerHTML = '<div class="scroll-progress"></div>';
    document.body.appendChild(scrollIndicator);

    const scrollProgress = scrollIndicator.querySelector('.scroll-progress');

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        scrollProgress.style.width = scrollPercent + '%';
    });
}

// Back to Top Button
function initBackToTop() {
    const backToTop = document.createElement('button');
    backToTop.className = 'back-to-top';
    backToTop.innerHTML = '<i class="bi bi-arrow-up"></i>';
    backToTop.setAttribute('aria-label', 'العودة إلى الأعلى');
    document.body.appendChild(backToTop);

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.classList.add('show');
        } else {
            backToTop.classList.remove('show');
        }
    });

    backToTop.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Parallax Effects
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.hero-carousel, .newsletter-section');

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;

        parallaxElements.forEach(element => {
            const rate = scrolled * -0.5;
            element.style.transform = `translateY(${rate}px)`;
        });
    });
}

// Counter Animations
function initCounterAnimations() {
    const counters = document.querySelectorAll('.achievement-item h3');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.textContent);
                let current = 0;
                const increment = target / 100;

                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.ceil(current);
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
                observer.unobserve(counter);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => observer.observe(counter));
}

// Advanced Animations
function initAdvancedAnimations() {
    // Stagger animations for product cards
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-on-scroll');
    });

    // Hover sound effects (optional)
    const interactiveElements = document.querySelectorAll('.product-card, .category-card, .btn');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            // Add subtle vibration on mobile
            if (navigator.vibrate) {
                navigator.vibrate(10);
            }
        });
    });
}

// Keyboard Navigation Enhancement
function initKeyboardNavigation() {
    // Add keyboard navigation for product cards
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach((card, index) => {
        card.setAttribute('tabindex', '0');
        card.setAttribute('role', 'button');

        card.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const link = card.querySelector('a');
                if (link) link.click();
            }

            // Arrow key navigation
            if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
                e.preventDefault();
                const direction = e.key === 'ArrowRight' ? 1 : -1;
                const nextIndex = (index + direction + productCards.length) % productCards.length;
                productCards[nextIndex].focus();
            }
        });
    });
}

// Enhanced Product Card Interactions
function enhanceProductCardInteractions() {
    const productCards = document.querySelectorAll('.product-card');

    productCards.forEach(card => {
        // Add ripple effect on click
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });

        // Add loading state for quick actions
        const quickActions = card.querySelectorAll('.quick-action-btn');
        quickActions.forEach(btn => {
            btn.addEventListener('click', function() {
                const originalContent = this.innerHTML;
                this.innerHTML = '<i class="bi bi-arrow-repeat spin"></i>';
                this.disabled = true;

                setTimeout(() => {
                    this.innerHTML = originalContent;
                    this.disabled = false;
                }, 1000);
            });
        });
    });
}

// Initialize enhanced interactions
document.addEventListener('DOMContentLoaded', function() {
    enhanceProductCardInteractions();
});

// Add CSS for ripple effect and spinner
const additionalStyles = `
<style>
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced focus styles for accessibility */
.product-card:focus,
.category-card:focus,
.btn:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .ripple,
    .spin,
    .animate-on-scroll {
        animation: none !important;
        transition: none !important;
    }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', additionalStyles);

// Advanced Performance Optimizations
function initPerformanceOptimizations() {
    // Preload critical resources
    preloadCriticalResources();

    // Lazy load images with intersection observer
    initAdvancedLazyLoading();

    // Optimize font loading
    optimizeFontLoading();

    // Cache management
    initCacheManagement();
}

function preloadCriticalResources() {
    // Preload critical CSS
    const criticalCSS = document.createElement('link');
    criticalCSS.rel = 'preload';
    criticalCSS.as = 'style';
    criticalCSS.href = 'assets/css/homepage.css';
    document.head.appendChild(criticalCSS);

    // Preload hero images
    const heroImages = document.querySelectorAll('.carousel-item img');
    heroImages.forEach((img, index) => {
        if (index < 2) { // Preload first 2 images only
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = img.src;
            document.head.appendChild(link);
        }
    });
}

function initAdvancedLazyLoading() {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;

                // Add loading animation
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.3s ease';

                // Load image
                const tempImg = new Image();
                tempImg.onload = () => {
                    img.src = tempImg.src;
                    img.style.opacity = '1';
                    img.classList.add('loaded');
                };
                tempImg.src = img.dataset.src || img.src;

                observer.unobserve(img);
            }
        });
    }, {
        rootMargin: '50px 0px',
        threshold: 0.01
    });

    document.querySelectorAll('img[loading="lazy"]').forEach(img => {
        imageObserver.observe(img);
    });
}

function optimizeFontLoading() {
    // Use font-display: swap for better performance
    const fontCSS = `
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
    `;

    const style = document.createElement('style');
    style.textContent = fontCSS;
    document.head.appendChild(style);
}

function initCacheManagement() {
    // Service worker for caching (if supported)
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js').catch(err => {
            console.log('Service Worker registration failed:', err);
        });
    }

    // Local storage management
    const cacheKeys = ['selectedRegion', 'userPreferences', 'cartItems'];
    cacheKeys.forEach(key => {
        const data = localStorage.getItem(key);
        if (data) {
            try {
                const parsed = JSON.parse(data);
                if (parsed.expiry && Date.now() > parsed.expiry) {
                    localStorage.removeItem(key);
                }
            } catch (e) {
                // Invalid JSON, remove it
                localStorage.removeItem(key);
            }
        }
    });
}

// Error Handling and User Feedback
function initErrorHandling() {
    // Global error handler
    window.addEventListener('error', (e) => {
        console.error('Global error:', e.error);
        showToast('حدث خطأ غير متوقع، يرجى إعادة تحميل الصفحة', 'error');
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (e) => {
        console.error('Unhandled promise rejection:', e.reason);
        showToast('حدث خطأ في التحميل، يرجى المحاولة مرة أخرى', 'warning');
    });

    // Network status monitoring
    window.addEventListener('online', () => {
        showToast('تم استعادة الاتصال بالإنترنت', 'success');
    });

    window.addEventListener('offline', () => {
        showToast('انقطع الاتصال بالإنترنت', 'warning');
    });
}

// User Experience Enhancements
function initUXEnhancements() {
    // Add loading states to all buttons
    document.querySelectorAll('button, .btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.disabled && !this.classList.contains('no-loading')) {
                this.classList.add('loading');
                setTimeout(() => {
                    this.classList.remove('loading');
                }, 2000);
            }
        });
    });

    // Add smooth transitions to all interactive elements
    const interactiveElements = document.querySelectorAll('a, button, .btn, .card, .product-card');
    interactiveElements.forEach(element => {
        element.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    });

    // Add focus management for modals
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.show');
            if (activeModal) {
                const closeBtn = activeModal.querySelector('[data-bs-dismiss="modal"]');
                if (closeBtn) closeBtn.click();
            }
        }
    });
}

// Initialize all enhancements
document.addEventListener('DOMContentLoaded', function() {
    initPerformanceOptimizations();
    initErrorHandling();
    initUXEnhancements();
});

// Add loading button styles
const loadingStyles = `
<style>
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn.loading:hover {
    transform: none !important;
}

/* Enhanced accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .product-card,
    .category-card,
    .btn {
        border: 2px solid currentColor !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .product-card,
    .category-card {
        background: #2c3e50 !important;
        color: white !important;
    }

    .section-title {
        color: white !important;
    }

    .section-subtitle {
        color: #bdc3c7 !important;
    }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', loadingStyles);
</script>

<?php require_once 'includes/footer.php'; ?>
