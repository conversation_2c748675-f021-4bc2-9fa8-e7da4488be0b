<?php
/**
 * Featured Products Validation Script - Version 2.0
 * Comprehensive testing for the rebuilt featured products section
 */

// Include configuration
require_once 'config/config.php';
require_once 'config/database.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>اختبار المنتجات المميزة المحدثة</title>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>\n";
echo "    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap' rel='stylesheet'>\n";
echo "    <link href='assets/css/featured-products.css' rel='stylesheet'>\n";
echo "    <style>\n";
echo "        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%); padding: 2rem 0; }\n";
echo "        .test-container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }\n";
echo "        .test-header { background: white; padding: 2rem; border-radius: 1rem; margin-bottom: 2rem; box-shadow: 0 10px 25px rgba(0,0,0,0.1); }\n";
echo "        .test-section { background: white; padding: 1.5rem; border-radius: 1rem; margin-bottom: 1.5rem; box-shadow: 0 4px 6px rgba(0,0,0,0.05); }\n";
echo "        .test-result { padding: 1rem; margin: 0.5rem 0; border-radius: 0.75rem; display: flex; align-items: center; gap: 0.5rem; }\n";
echo "        .test-success { background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); color: #155724; border: 1px solid #c3e6cb; }\n";
echo "        .test-error { background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); color: #721c24; border: 1px solid #f5c6cb; }\n";
echo "        .test-warning { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); color: #856404; border: 1px solid #ffeaa7; }\n";
echo "        .test-info { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); color: #0c5460; border: 1px solid #bee5eb; }\n";
echo "        .test-icon { font-size: 1.2rem; }\n";
echo "        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0; }\n";
echo "        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; border-radius: 0.75rem; text-align: center; }\n";
echo "        .stat-number { font-size: 2rem; font-weight: 800; margin-bottom: 0.5rem; }\n";
echo "        .stat-label { font-size: 0.9rem; opacity: 0.9; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='test-container'>\n";
echo "    <div class='test-header text-center'>\n";
echo "        <h1 class='mb-3'>🚀 اختبار المنتجات المميزة المحدثة</h1>\n";
echo "        <p class='text-muted'>فحص شامل للتصميم الجديد والوظائف المحسنة - الإصدار 2.0</p>\n";
echo "    </div>\n";

// Test Statistics
$totalTests = 0;
$passedTests = 0;
$failedTests = 0;
$warningTests = 0;

function runTest($testName, $condition, $successMsg, $failMsg, $isWarning = false) {
    global $totalTests, $passedTests, $failedTests, $warningTests;
    $totalTests++;
    
    if ($condition) {
        $passedTests++;
        $class = 'test-success';
        $icon = '✅';
        $message = $successMsg;
    } else {
        if ($isWarning) {
            $warningTests++;
            $class = 'test-warning';
            $icon = '⚠️';
        } else {
            $failedTests++;
            $class = 'test-error';
            $icon = '❌';
        }
        $message = $failMsg;
    }
    
    echo "    <div class='test-result {$class}'>\n";
    echo "        <span class='test-icon'>{$icon}</span>\n";
    echo "        <div><strong>{$testName}:</strong> {$message}</div>\n";
    echo "    </div>\n";
    
    return $condition;
}

// Test Section 1: File Structure
echo "    <div class='test-section'>\n";
echo "        <h3 class='mb-3'>📁 اختبار هيكل الملفات</h3>\n";

runTest(
    'اختبار 1.1', 
    file_exists('assets/css/featured-products.css'),
    'ملف CSS موجود ومتاح',
    'ملف CSS غير موجود'
);

runTest(
    'اختبار 1.2', 
    file_exists('index.php'),
    'ملف الصفحة الرئيسية موجود',
    'ملف الصفحة الرئيسية غير موجود'
);

echo "    </div>\n";

// Test Section 2: CSS Content Analysis
echo "    <div class='test-section'>\n";
echo "        <h3 class='mb-3'>🎨 تحليل محتوى CSS</h3>\n";

$cssContent = file_exists('assets/css/featured-products.css') ? file_get_contents('assets/css/featured-products.css') : '';
$cssSize = strlen($cssContent);

runTest(
    'اختبار 2.1', 
    strpos($cssContent, ':root') !== false,
    'متغيرات CSS موجودة (CSS Custom Properties)',
    'متغيرات CSS غير موجودة'
);

runTest(
    'اختبار 2.2', 
    strpos($cssContent, '.featured-products') !== false,
    'فئة القسم الرئيسية موجودة',
    'فئة القسم الرئيسية غير موجودة'
);

runTest(
    'اختبار 2.3', 
    strpos($cssContent, '.product-card') !== false,
    'فئة بطاقة المنتج الجديدة موجودة',
    'فئة بطاقة المنتج الجديدة غير موجودة'
);

runTest(
    'اختبار 2.4', 
    strpos($cssContent, 'direction: rtl') !== false,
    'دعم RTL مفعل',
    'دعم RTL غير مفعل'
);

runTest(
    'اختبار 2.5', 
    strpos($cssContent, '@media') !== false,
    'استعلامات الوسائط للتصميم المتجاوب موجودة',
    'استعلامات الوسائط غير موجودة'
);

runTest(
    'اختبار 2.6', 
    strpos($cssContent, 'prefers-reduced-motion') !== false,
    'دعم إمكانية الوصول (Reduced Motion) موجود',
    'دعم إمكانية الوصول غير موجود'
);

runTest(
    'اختبار 2.7', 
    $cssSize > 15000,
    "حجم ملف CSS مناسب ({$cssSize} حرف)",
    "حجم ملف CSS صغير جداً ({$cssSize} حرف)",
    true
);

echo "    </div>\n";
