<?php
require_once __DIR__ . '/../config/config.php';

$siteName = getSetting('site_name');
$cartCount = getCartItemCount();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="theme-color" content="#667eea">
    <meta name="color-scheme" content="light dark">
    <meta name="format-detection" content="telephone=no">

    <!-- Performance Hints -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="referrer" content="strict-origin-when-cross-origin">

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="<?php echo SITE_NAME; ?>">

    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . $siteName : $siteName; ?></title>
    <meta name="description" content="<?php echo getSetting('site_description'); ?>">
    <meta name="keywords" content="<?php echo getSetting('site_keywords'); ?>">
    <meta name="author" content="<?php echo $siteName; ?>">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($pageTitle) ? $pageTitle . ' - ' . $siteName : $siteName; ?>">
    <meta property="og:description" content="<?php echo getSetting('site_description'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:site_name" content="<?php echo $siteName; ?>">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="assets/css/homepage.css" as="style">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Google Fonts with font-display: swap -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="mask-icon" href="assets/images/safari-pinned-tab.svg" color="#667eea">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Homepage Styles -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'index.php'): ?>
    <link href="<?php echo SITE_URL; ?>/assets/css/homepage.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/assets/css/hero-carousel.css" rel="stylesheet">

    <!-- Structured Data for Homepage -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "<?php echo htmlspecialchars($siteName); ?>",
        "url": "<?php echo SITE_URL; ?>",
        "description": "<?php echo htmlspecialchars(getSetting('site_description')); ?>",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "<?php echo SITE_URL; ?>/search.php?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    <?php endif; ?>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #2c3e50 !important;
        }
        
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }
        
        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: #ffd700 !important;
            transform: translateY(-2px);
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cart-icon {
            position: relative;
            display: inline-block;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .product-card .card-img-top {
            height: 250px;
            object-fit: cover;
            border-radius: 15px 15px 0 0;
        }
        
        .price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .original-price {
            text-decoration: line-through;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .search-form {
            max-width: 280px;
        }

        .search-form .input-group {
            min-width: 200px;
        }

        .search-form .form-control {
            border-radius: 0.375rem 0 0 0.375rem;
            border-left: 1px solid rgba(255,255,255,0.5);
        }

        .search-form .btn {
            border-radius: 0 0.375rem 0.375rem 0;
            border-right: 1px solid rgba(255,255,255,0.5);
        }

        /* Enhanced Search Bar Styling */
        .navbar-search {
            position: relative;
            min-width: 300px;
        }

        .search-form {
            position: relative;
        }

        .search-input {
            background: rgba(255,255,255,0.15);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 25px 0 0 25px;
            padding: 0.5rem 1rem;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .search-input:focus {
            background: rgba(255,255,255,0.25);
            border-color: rgba(255,255,255,0.5);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
        }

        .btn-search {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-left: none;
            color: white;
            border-radius: 0 25px 25px 0;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .btn-search:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: scale(1.05);
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .search-suggestion {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-suggestion:hover {
            background-color: #f8f9fa;
        }

        .search-suggestion:last-child {
            border-bottom: none;
        }

        .navbar-nav .nav-link {
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }
        
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.2rem;
            }
            
            .hero-section {
                padding: 40px 0;
            }
            
            .search-form {
                max-width: 100%;
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Professional Page Loader (Homepage Only) -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'index.php'): ?>
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h4>جاري التحميل...</h4>
            <p>يرجى الانتظار</p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Floating WhatsApp Button -->
    <div class="floating-whatsapp" id="floatingWhatsApp">
        <a href="https://wa.me/<?php echo htmlspecialchars(getSetting('whatsapp_number') ?? '9647700000000'); ?>?text=مرحباً، أريد الاستفسار عن منتجاتكم"
           target="_blank"
           class="whatsapp-btn"
           aria-label="تواصل معنا عبر واتساب">
            <i class="bi bi-whatsapp"></i>
            <span class="whatsapp-text">تواصل معنا</span>
        </a>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <i class="bi bi-shop"></i> <?php echo $siteName; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>">
                            <i class="bi bi-house"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/products.php">
                            <i class="bi bi-grid"></i> المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/offers.php">
                            <i class="bi bi-percent"></i> العروض
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/guidelines.php">
                            <i class="bi bi-lightbulb"></i> الإرشادات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/influencers.php">
                            <i class="bi bi-people"></i> المؤثرين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>/contact.php">
                            <i class="bi bi-envelope"></i> اتصل بنا
                        </a>
                    </li>
                </ul>

                <!-- Search Bar -->
                <div class="navbar-search mx-3">
                    <form class="search-form" action="<?php echo SITE_URL; ?>/search.php" method="GET">
                        <div class="input-group">
                            <input type="text"
                                   class="form-control search-input"
                                   name="q"
                                   placeholder="ابحث عن المنتجات..."
                                   value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>"
                                   autocomplete="off">
                            <button class="btn btn-search" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </form>
                </div>

                <!-- Right side navigation items -->
                <div class="d-flex align-items-center">
                    <!-- Cart -->
                    <a href="<?php echo SITE_URL; ?>/cart.php" class="btn btn-outline-light position-relative me-3">
                        <span class="cart-icon">
                            <i class="bi bi-cart3"></i>
                            <?php if ($cartCount > 0): ?>
                                <span class="cart-badge"><?php echo $cartCount; ?></span>
                            <?php endif; ?>
                        </span>
                        <span class="d-none d-md-inline">سلة التسوق</span>
                    </a>

                    <!-- Search Form -->
                    <form class="d-flex search-form" method="GET" action="<?php echo SITE_URL; ?>/products.php">
                        <div class="input-group">
                            <input class="form-control" type="search" name="search" placeholder="البحث..."
                                   value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            <button class="btn btn-outline-light" type="submit" title="البحث">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    <?php if (isset($_SESSION['message'])): ?>
        <div class="container mt-3">
            <?php 
            echo showMessage($_SESSION['message']['text'], $_SESSION['message']['type']);
            unset($_SESSION['message']);
            ?>
        </div>
    <?php endif; ?>
